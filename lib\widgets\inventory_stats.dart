
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/inventory_service.dart';

class InventoryStats extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<InventoryService>(
      builder: (context, service, child) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Items',
                  service.totalItems.toString(),
                  Icons.inventory,
                  Colors.blue[700]!,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Total Value',
                  '\$${service.totalInventoryValue.toStringAsFixed(2)}',
                  Icons.attach_money,
                  Colors.green[700]!,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Categories',
                  service.itemsByCategory.length.toString(),
                  Icons.category,
                  Colors.orange[700]!,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Container(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
