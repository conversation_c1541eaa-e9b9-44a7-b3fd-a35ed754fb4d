
// services/inventory_service.dart
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/inventory_item.dart';

class InventoryService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'inventory';

  List<InventoryItem> _items = [];
  bool _isLoading = false;
  String _searchQuery = '';

  List<InventoryItem> get items => _searchQuery.isEmpty
      ? _items
      : _items.where((item) =>
          item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          item.category.toLowerCase().contains(_searchQuery.toLowerCase())).toList();

  bool get isLoading => _isLoading;

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  Future<void> fetchItems() async {
    _isLoading = true;
    notifyListeners();

    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('updatedAt', descending: true)
          .get();

      _items = querySnapshot.docs
          .map((doc) => InventoryItem.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      print('Error fetching items: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addItem(InventoryItem item) async {
    try {
      await _firestore.collection(_collection).add(item.toMap());
      await fetchItems();
      return true;
    } catch (e) {
      print('Error adding item: $e');
      return false;
    }
  }

  Future<bool> updateItem(InventoryItem item) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(item.id)
          .update(item.toMap());
      await fetchItems();
      return true;
    } catch (e) {
      print('Error updating item: $e');
      return false;
    }
  }

  Future<bool> deleteItem(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
      await fetchItems();
      return true;
    } catch (e) {
      print('Error deleting item: $e');
      return false;
    }
  }

  double get totalInventoryValue {
    return _items.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  }

  int get totalItems {
    return _items.fold(0, (sum, item) => sum + item.quantity);
  }

  Map<String, int> get itemsByCategory {
    final Map<String, int> categoryCount = {};
    for (final item in _items) {
      categoryCount[item.category] = (categoryCount[item.category] ?? 0) + 1;
    }
    return categoryCount;
  }
}
